/* Reset e base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Outfit', sans-serif;
  line-height: 1.6;
  color: #1A1A1A;
  background-color: #F9F9F7;
}

.App {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.header {
  background-color: #F2F6FC;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(29, 63, 134, 0.1);
}

.nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  height: 60px;
  width: auto;
}

.site-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1D3F86;
  margin: 0;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #1D3F86 0%, #0B8A29 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.3rem;
  font-weight: 400;
  margin-bottom: 2.5rem;
  opacity: 0.95;
}

/* CTA Buttons */
.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: 'Outfit', sans-serif;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cta-button.primary {
  background-color: #F5B400;
  color: #1A1A1A;
}

.cta-button.primary:hover {
  background-color: #e6a200;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 180, 0, 0.3);
}

.cta-button.secondary {
  background-color: #1D3F86;
  color: white;
  border: 2px solid white;
}

.cta-button.secondary:hover {
  background-color: white;
  color: #1D3F86;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* Sections */
.about, .services, .contact {
  padding: 4rem 0;
}

.about {
  background-color: #F2F6FC;
}

.services {
  background-color: #F9F9F7;
}

.contact {
  background-color: #F2F6FC;
}

.section-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1D3F86;
  margin-bottom: 1.5rem;
  text-align: center;
}

.section-text {
  font-size: 1.2rem;
  color: #5A5A5A;
  margin-bottom: 2rem;
  line-height: 1.8;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.service-card {
  background-color: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(29, 63, 134, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(29, 63, 134, 0.15);
}

.service-card h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1D3F86;
  margin-bottom: 1rem;
}

.service-card p {
  color: #5A5A5A;
  font-size: 1rem;
  line-height: 1.6;
}

/* Footer */
.footer {
  background-color: #1D3F86;
  color: white;
  padding: 2rem 0;
  text-align: center;
}

.footer p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
  }

  .nav {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .site-title {
    font-size: 1.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 3rem 0;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .about, .services, .contact {
    padding: 3rem 0;
  }

  .service-card {
    padding: 1.5rem;
  }
}
