import './App.css';

function App() {
  const openEvent = () => {
    window.open('/allegati/Evento.jpg', '_blank');
  };

  const openPlanning = () => {
    window.open('/allegati/planning.pdf', '_blank');
  };

  return (
    <div className="App">
      {/* Header */}
      <header className="header">
        <div className="container">
          <div className="nav">
            <img src="/allegati/logo.png" alt="Due Ponti Sport Village" className="logo" />
            <h1 className="site-title">Due Ponti Sport Village</h1>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <h2 className="hero-title">Benvenuti al Due Ponti Sport Village</h2>
            <p className="hero-subtitle">
              Il tuo villaggio sportivo di riferimento dove passione e divertimento si incontrano
            </p>
            <div className="cta-buttons">
              <button onClick={openEvent} className="cta-button primary">
                Visualizza Evento
              </button>
              <button onClick={openPlanning} className="cta-button secondary">
                Scarica Planning
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="about">
        <div className="container">
          <div className="section-content">
            <h3 className="section-title">Chi Siamo</h3>
            <p className="section-text">
              Due Ponti Sport Village è il luogo ideale per chi ama lo sport e il divertimento.
              Offriamo strutture moderne e servizi di qualità per soddisfare ogni esigenza sportiva.
            </p>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="services">
        <div className="container">
          <h3 className="section-title">I Nostri Servizi</h3>
          <div className="services-grid">
            <div className="service-card">
              <h4>Campi Sportivi</h4>
              <p>Campi da calcio, tennis, basket e molto altro</p>
            </div>
            <div className="service-card">
              <h4>Fitness Center</h4>
              <p>Palestra attrezzata con i migliori macchinari</p>
            </div>
            <div className="service-card">
              <h4>Piscina</h4>
              <p>Piscina olimpionica per nuoto e acqua fitness</p>
            </div>
            <div className="service-card">
              <h4>Eventi</h4>
              <p>Organizzazione di tornei e eventi sportivi</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="contact">
        <div className="container">
          <div className="section-content">
            <h3 className="section-title">Contattaci</h3>
            <p className="section-text">
              Vieni a trovarci o contattaci per maggiori informazioni sui nostri servizi e le nostre attività.
            </p>
            <div className="cta-buttons">
              <button onClick={openEvent} className="cta-button primary">
                Visualizza Evento
              </button>
              <button onClick={openPlanning} className="cta-button secondary">
                Scarica Planning
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <p>&copy; 2024 Due Ponti Sport Village. Tutti i diritti riservati.</p>
        </div>
      </footer>
    </div>
  );
}

export default App;
